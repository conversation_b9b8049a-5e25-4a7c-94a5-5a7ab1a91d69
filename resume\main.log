This is XeTeX, Version 3.141592653-2.6-0.999995 (MiKTeX 24.1) (preloaded format=xelatex 2025.7.26)  26 JUL 2025 22:24
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**c:/Users/<USER>/Desktop/resume/resume/main.tex
(c:/Users/<USER>/Desktop/resume/resume/main.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(resume.cls (E:\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-01-04 L3 programming layer (loader) 
 (E:\MiKTeX\tex/latex/l3backend\l3backend-xetex.def
File: l3backend-xetex.def 2024-01-04 L3 backend support: XeTeX
\g__graphics_track_int=\count183
\l__pdf_internal_box=\box51
\g__pdf_backend_object_int=\count184
\g__pdf_backend_annotation_int=\count185
\g__pdf_backend_link_int=\count186
)) (E:\MiKTeX\tex/latex/l3packages/l3keys2e\l3keys2e.sty
Package: l3keys2e 2023-10-10 LaTeX2e option processing using LaTeX3 keys
)
Document Class: resume 2022-12-26 v0.1.0 Another Resume Class by Feng Kaiyu
(E:\MiKTeX\tex/latex/ctex\ctexart.cls (E:\MiKTeX\tex/latex/ctex/config\ctexbackend.cfg
File: ctexbackend.cfg 2022/07/14 v2.5.10 Backend configuration file (CTEX)
)
Document Class: ctexart 2022/07/14 v2.5.10 Chinese adapter for class article (CTEX)
(E:\MiKTeX\tex/latex/ctex\ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (E:\MiKTeX\tex/latex/ctex\ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
) (E:\MiKTeX\tex/latex/base\fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (E:\MiKTeX\tex/latex/base\ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count187
\l__ctex_tmp_box=\box52
\l__ctex_tmp_dim=\dimen140
\g__ctex_section_depth_int=\count188
\g__ctex_font_size_int=\count189
 (E:\MiKTeX\tex/latex/ctex/config\ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
) (E:\MiKTeX\tex/latex/base\article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(E:\MiKTeX\tex/latex/base\size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count190
\c@section=\count191
\c@subsection=\count192
\c@subsubsection=\count193
\c@paragraph=\count194
\c@subparagraph=\count195
\c@figure=\count196
\c@table=\count197
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen141
) (E:\MiKTeX\tex/latex/ctex/engine\ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)
 (E:\MiKTeX\tex/xelatex/xecjk\xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX
 (E:\MiKTeX\tex/latex/l3packages/xtemplate\xtemplate.sty
Package: xtemplate 2023-10-10 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen142
\l__xtemplate_tmp_int=\count198
\l__xtemplate_tmp_muskip=\muskip16
\l__xtemplate_tmp_skip=\skip50
)
\l__xeCJK_tmp_int=\count199
\l__xeCJK_tmp_box=\box53
\l__xeCJK_tmp_dim=\dimen143
\l__xeCJK_tmp_skip=\skip51
\g__xeCJK_space_factor_int=\count266
\l__xeCJK_begin_int=\count267
\l__xeCJK_end_int=\count268
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip52
\c__xeCJK_none_node=\count269
\g__xeCJK_node_int=\count270
\c__xeCJK_CJK_node_dim=\dimen144
\c__xeCJK_CJK-space_node_dim=\dimen145
\c__xeCJK_default_node_dim=\dimen146
\c__xeCJK_CJK-widow_node_dim=\dimen147
\c__xeCJK_normalspace_node_dim=\dimen148
\c__xeCJK_default-space_node_skip=\skip53
\l__xeCJK_ccglue_skip=\skip54
\l__xeCJK_ecglue_skip=\skip55
\l__xeCJK_punct_kern_skip=\skip56
\l__xeCJK_indent_box=\box54
\l__xeCJK_last_penalty_int=\count271
\l__xeCJK_last_bound_dim=\dimen149
\l__xeCJK_last_kern_dim=\dimen150
\l__xeCJK_widow_penalty_int=\count272

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen151
\l__xeCJK_mixed_punct_width_dim=\dimen152
\l__xeCJK_middle_punct_width_dim=\dimen153
\l__xeCJK_fixed_margin_width_dim=\dimen154
\l__xeCJK_mixed_margin_width_dim=\dimen155
\l__xeCJK_middle_margin_width_dim=\dimen156
\l__xeCJK_bound_punct_width_dim=\dimen157
\l__xeCJK_bound_margin_width_dim=\dimen158
\l__xeCJK_margin_minimum_dim=\dimen159
\l__xeCJK_kerning_total_width_dim=\dimen160
\l__xeCJK_same_align_margin_dim=\dimen161
\l__xeCJK_different_align_margin_dim=\dimen162
\l__xeCJK_kerning_margin_width_dim=\dimen163
\l__xeCJK_kerning_margin_minimum_dim=\dimen164
\l__xeCJK_bound_dim=\dimen165
\l__xeCJK_reverse_bound_dim=\dimen166
\l__xeCJK_margin_dim=\dimen167
\l__xeCJK_minimum_bound_dim=\dimen168
\l__xeCJK_kerning_margin_dim=\dimen169
\g__xeCJK_family_int=\count273
\l__xeCJK_fam_int=\count274
\g__xeCJK_fam_allocation_int=\count275
\l__xeCJK_verb_case_int=\count276
\l__xeCJK_verb_exspace_skip=\skip57
 (E:\MiKTeX\tex/latex/fontspec\fontspec.sty (E:\MiKTeX\tex/latex/l3packages/xparse\xparse.sty
Package: xparse 2023-10-10 L3 Experimental document command parser
)
Package: fontspec 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
 (E:\MiKTeX\tex/latex/fontspec\fontspec-xetex.sty
Package: fontspec-xetex 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count277
\l__fontspec_language_int=\count278
\l__fontspec_strnum_int=\count279
\l__fontspec_tmp_int=\count280
\l__fontspec_tmpa_int=\count281
\l__fontspec_tmpb_int=\count282
\l__fontspec_tmpc_int=\count283
\l__fontspec_em_int=\count284
\l__fontspec_emdef_int=\count285
\l__fontspec_strong_int=\count286
\l__fontspec_strongdef_int=\count287
\l__fontspec_tmpa_dim=\dimen170
\l__fontspec_tmpb_dim=\dimen171
\l__fontspec_tmpc_dim=\dimen172
 (E:\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (E:\MiKTeX\tex/latex/fontspec\fontspec.cfg))) (E:\MiKTeX\tex/xelatex/xecjk\xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen173
\l__ctex_ccglue_skip=\skip58
)
\l__ctex_ziju_dim=\dimen174
 (E:\MiKTeX\tex/latex/zhnumber\zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count288
\l__zhnum_tmp_int=\count289
 (E:\MiKTeX\tex/latex/zhnumber\zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
))
\l__ctex_heading_skip=\skip59
 (E:\MiKTeX\tex/latex/ctex/scheme\ctex-scheme-chinese-article.def
File: ctex-scheme-chinese-article.def 2022/07/14 v2.5.10 Chinese scheme for article (CTEX)
 (E:\MiKTeX\tex/latex/ctex/config\ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
)) (E:\MiKTeX\tex/latex/ctex\ctex-c5size.clo
File: ctex-c5size.clo 2022/07/14 v2.5.10 c5size option (CTEX)
) (E:\MiKTeX\tex/latex/ctex/fontset\ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTEX)
)) (E:\MiKTeX\tex/latex/ctex/config\ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
) (E:\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (E:\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (E:\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (E:\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count290
\Gm@cntv=\count291
\c@Gm@tempcnt=\count292
\Gm@bindingoffset=\dimen175
\Gm@wd@mp=\dimen176
\Gm@odd@mp=\dimen177
\Gm@even@mp=\dimen178
\Gm@layoutwidth=\dimen179
\Gm@layoutheight=\dimen180
\Gm@layouthoffset=\dimen181
\Gm@layoutvoffset=\dimen182
\Gm@dimlist=\toks18
 (E:\MiKTeX\tex/latex/geometry\geometry.cfg)) (E:\MiKTeX\tex/latex/fancyhdr\fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers
\f@nch@headwidth=\skip60
\f@nch@offset@elh=\skip61
\f@nch@offset@erh=\skip62
\f@nch@offset@olh=\skip63
\f@nch@offset@orh=\skip64
\f@nch@offset@elf=\skip65
\f@nch@offset@erf=\skip66
\f@nch@offset@olf=\skip67
\f@nch@offset@orf=\skip68
\f@nch@height=\skip69
\f@nch@footalignment=\skip70
\f@nch@widthL=\skip71
\f@nch@widthC=\skip72
\f@nch@widthR=\skip73
\@temptokenb=\toks19
) (E:\MiKTeX\tex/latex/enumitem\enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip74
\enit@outerparindent=\dimen183
\enit@toks=\toks20
\enit@inbox=\box55
\enit@count@id=\count293
\enitdp@description=\count294
) (E:\MiKTeX\tex/latex/footmisc\footmisc.sty
Package: footmisc 2025/05/09 v7.0b a miscellany of footnote facilities
 (E:\MiKTeX\tex/latex/footmisc\footmisc-2022-02-14.sty
Package: footmisc 2024/12/24 v6.0g a miscellany of footnote facilities
\FN@temptoken=\toks21
\footnotemargin=\dimen184
\@outputbox@depth=\dimen185
\FN@tempboxb=\box56
\FN@tempboxc=\box57
\footglue=\skip75
\footnotebaselineskip=\dimen186
Package footmisc Info: Declaring symbol style bringhurst on input line 699.
Package footmisc Info: Declaring symbol style chicago on input line 707.
Package footmisc Info: Declaring symbol style wiley on input line 716.
Package footmisc Info: Declaring symbol style lamport-robust on input line 727.
Package footmisc Info: Declaring symbol style lamport* on input line 747.
Package footmisc Info: Declaring symbol style lamport*-robust on input line 768.
)) (E:\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2023-11-26 v7.01g Hypertext links for LaTeX
 (E:\MiKTeX\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (E:\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (E:\MiKTeX\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (E:\MiKTeX\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (E:\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (E:\MiKTeX\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (E:\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (E:\MiKTeX\tex/latex/letltxmacro\letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
) (E:\MiKTeX\tex/latex/auxhook\auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (E:\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (E:\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (E:\MiKTeX\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (E:\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count295
) (E:\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count296
)
\@linkdim=\dimen187
\Hy@linkcounter=\count297
\Hy@pagecounter=\count298
 (E:\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2023-11-26 v7.01g Hyperref: PDFDocEncoding definition (HO)
) (E:\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count299
 (E:\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2023-11-26 v7.01g Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4064.
Package hyperref Info: Option `colorlinks' set `false' on input line 4064.
Package hyperref Info: Hyper figures OFF on input line 4181.
Package hyperref Info: Link nesting OFF on input line 4186.
Package hyperref Info: Hyper index ON on input line 4189.
Package hyperref Info: Plain pages OFF on input line 4196.
Package hyperref Info: Backreferencing OFF on input line 4201.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4448.
\c@Hy@tempcnt=\count300
 (E:\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4786.
\XeTeXLinkMargin=\dimen188
 (E:\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (E:\MiKTeX\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count301
\Field@Width=\dimen189
\Fld@charsize=\dimen190
Package hyperref Info: Hyper figures OFF on input line 6065.
Package hyperref Info: Link nesting OFF on input line 6070.
Package hyperref Info: Hyper index ON on input line 6073.
Package hyperref Info: backreferencing OFF on input line 6080.
Package hyperref Info: Link coloring OFF on input line 6085.
Package hyperref Info: Link coloring with OCG OFF on input line 6090.
Package hyperref Info: PDF/A mode OFF on input line 6095.
 (E:\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count302
\c@Item=\count303
\c@Hfootnote=\count304
)
Package hyperref Info: Driver (autodetected): hxetex.
 (E:\MiKTeX\tex/latex/hyperref\hxetex.def
File: hxetex.def 2023-11-26 v7.01g Hyperref driver for XeTeX
 (E:\MiKTeX\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box58
\c@Hy@AnnotLevel=\count305
\HyField@AnnotCount=\count306
\Fld@listcount=\count307
\c@bookmark@seq@number=\count308
 (E:\MiKTeX\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)
 (E:\MiKTeX\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 284.
)
\Hy@SectionHShift=\skip76
) (E:\MiKTeX\tex/xelatex/xecjk\xeCJKfntef.sty
Package: xeCJKfntef 2022/08/05 v3.9.1 xeCJK font effect
 (E:\MiKTeX\tex/latex/ulem\ulem.sty
\UL@box=\box59
\UL@hyphenbox=\box60
\UL@skip=\skip77
\UL@hook=\toks22
\UL@height=\dimen191
\UL@pe=\count309
\UL@pixel=\dimen192
\ULC@box=\box61
Package: ulem 2019/11/18
\ULdepth=\dimen193
)
\l__xeCJK_space_skip=\skip78
\c__xeCJK_ulem-begin_node_dim=\dimen194
\l__xeCJK_hidden_box=\box62
\l__xeCJK_fntef_box=\box63
\l__xeCJK_under_symbol_box=\box64
\c__xeCJK_filll_skip=\skip79
) (E:\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (E:\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (E:\MiKTeX\tex/latex/graphics-def\xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
) (E:\MiKTeX\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
\c@resumebookmark=\count310
) (E:\MiKTeX\tex/latex/setspace\setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
) (main.aux)
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
\symlegacymaths=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 17.
LaTeX Font Info:    Redeclaring math accent \acute on input line 17.
LaTeX Font Info:    Redeclaring math accent \grave on input line 17.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 17.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 17.
LaTeX Font Info:    Redeclaring math accent \bar on input line 17.
LaTeX Font Info:    Redeclaring math accent \breve on input line 17.
LaTeX Font Info:    Redeclaring math accent \check on input line 17.
LaTeX Font Info:    Redeclaring math accent \hat on input line 17.
LaTeX Font Info:    Redeclaring math accent \dot on input line 17.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 17.
LaTeX Font Info:    Redeclaring math symbol \colon on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 17.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 17.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 17.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 17.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 17.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 17.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 17.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 17.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 17.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(28.45274pt, 540.60239pt, 28.45274pt)
* v-part:(T,H,B)=(28.45274pt, 802.36774pt, 14.22636pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=540.60239pt
* \textheight=802.36774pt
* \oddsidemargin=-43.81725pt
* \evensidemargin=-43.81725pt
* \topmargin=-80.81725pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 17.
(main.out) (main.out)
\@outlinefile=\write3
\openout3 = `main.out'.



Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `\ResumeUrl' on input line 54.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `\ResumeUrl' on input line 79.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `\ResumeUrl' on input line 103.

[1

] (main.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2022/08/05>
 ***********
Package rerunfilecheck Info: File `main.out' has not changed.
(rerunfilecheck)             Checksum: E0E0A88AD1C654A220350448EE07A54B;1009.
 ) 
Here is how much of TeX's memory you used:
 16554 strings out of 409617
 368194 string characters out of 5781118
 1988191 words of memory out of 5000000
 38468 multiletter control sequences out of 15000+600000
 560409 words of font info for 69 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 107i,5n,111p,1248b,390s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on main.xdv (1 page, 44868 bytes).
