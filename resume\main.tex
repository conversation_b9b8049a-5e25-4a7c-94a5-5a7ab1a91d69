% !TeX TS-program = xelatex

\documentclass{resume}
\ResumeName{朱祉睿}

% 如果想插入照片，请使用以下两个库。
% \usepackage{graphicx}
% \usepackage{tikz}

% 压缩版面设置
\usepackage{setspace}
\setstretch{0.9}  % 进一步减少行距
\setlength{\parskip}{0.1em}  % 进一步减少段落间距
\setlength{\itemsep}{0pt}  % 减少列表项间距
\setlength{\parsep}{0pt}   % 减少段落解析间距

\begin{document}

\ResumeContacts{
  18229395676,%
  \ResumeUrl{mailto:<EMAIL>}{<EMAIL>},%
  算法实习生,%
  26届硕士在读%
}

% 如果想插入照片，请取消此代码的注释。
% 但是默认不推荐插入照片，因为这不是简历的重点。
% 如果默认的照片插入格式不能满足你的需求，你可以尝试调整照片的大小，或者使用其他的插入照片的方法。
% 不然，也可以先渲染 PDF 简历，然后用其他工具在 PDF 上叠加照片。
% \begin{tikzpicture}[remember picture, overlay]
%   \node [anchor=north east, inner sep=1cm]  at (current page.north east) 
%      {\includegraphics[width=2cm]{image.png}};
% \end{tikzpicture}

\ResumeTitle

\section{教育背景}
\ResumeItem
[中山大学|硕士研究生]
{\ResumeUrl{https://www.sysu.edu.cn}{中山大学}}
[\textnormal{应用统计（数学学院）|} 硕士（保研）]
[2024.09—2026.06]

\ResumeItem
[南方科技大学|本科生]
{\ResumeUrl{https://www.sustech.edu.cn}{南方科技大学}}
[\textnormal{统计学（统计与数据科学学院）|} 本科]
[2020.09—2024.06]

\section{实习经历}

\ResumeItem{\ResumeUrl{https://www.kuaishou.com}{快手}}
[商业化 广告算法实习]
[2025.03—2025.08]

\textbf{搜索广告精排表单CVR模型消偏：}
现有CVR模型将信息流与搜索广告样本混合训练，且将"表单停留时长"这一强相关信号仅作>1s的粗粒度二分类处理，产生如下主要问题：1. 损失了大量时序信息。2. 搜索与信息流样本比例差距悬殊，以及两者转化链路区别，导致模型线上在搜索场景下预估存在偏差。技术方案:
\begin{itemize}
  \item \textbf{精细化时长建模：} 引入保序回归：构建停留时长精细化建模辅助任务将连续时长划分桶，对桶间时序依赖进行建模，输出的概率向量作为特征融入主塔。
  \item \textbf{引入子任务：} 停留时长有大量值为0的样本出现，参考Google ZILN思想，单独建模网络预测停留时长是否大于0。其预测概率作为时序依赖马尔可夫链路的起始概率。
  \item \textbf{设计场景偏差网络：} 为解决搜索场景样本稀疏的问题，通过对信息流样本进行随机下采样平衡多源数据贡献。同时，设计 Bias 网络，使用 Search 特征，通过门控机制动态融入主干网络。
\end{itemize}

项目成果：闭环离线AUC +0.5\%。全量上线后，搜索表单场景核心转化数提升+3.2\%，客户预期花费\textbf{+4.08\%}，显著优化了广告投放效率。

\textbf{基于残差建模的直播实时发券模型：}
在 QCPX 广告拍卖机制下，将部分广告预算转为优惠券发放给价格敏感用户以提升曝光转化率，同时提升平台变现效率和客户跑量。技术方案
\begin{itemize}
  \item \textbf{Uplift建模:} 设计残差弹性网络，实时预估多面额发券对CVR的增量价值。进行严格的RCT实验，以AUUC等指标评估模型因果效应排序能力(模型 1.59 vs 随机0.53)。
  \item \textbf{连续值预估:} 使用改进的 Youtube Weighted LR 损失函数,实现对增量订单数（率）的无偏预估。
  \item \textbf{Pacing策略:} 解带mROI约束的优化问题，进而通过调节超参数影响最优面额求解，实现对mROI的精准Pacing，实现平台收益最大化。
\end{itemize}

项目成果：
模型在达成2.14的mROI的同时，为核心电商业务带来\textbf{+1.93\%的平台收入增长，并显著降低1.17\%}的广告成本。

\ResumeItem{\ResumeUrl{https://www.bytedance.com}{字节跳动}}
[业务中台 大模型算法实习]
[2024.9—2024.12]

\textbf{基于 LLM 的 AI LQA功能效果调优：}
背景: 字节跳动 STARLING 平台的 AI LQA 功能旨在自动进行翻译质检，但因准确率低 (基线合格率<65\%) 且误报严重，阻碍了 TikTok、PDI 等多业务线的有效应用及自动化流程。技术方案:
\begin{itemize}
  \item \textbf{数据工程与增强：} 利用平台的LQA 数据，借助 Gemini-2.5-Pro 模型及人工反馈，构造COT数据，为训练样本生成精细化的错误分析论证过程。应用 Pairwise 数据构建策略，将同一原文的参考翻译与错误翻译配对输入，强化模型对正负样本的对比学习能力。针对复杂错误类型如词汇遗漏、词汇不当等与数据不平衡问题，采用投票机制筛选高质量 COT 数据。
  
  \item \textbf{模型训练与优化：} 对 Qwen2.5-7B-Instruct 进行基于 LoRA 的 SFT ，优化模型输出包含判错结果及结构化推理过程的响应，提升结果可靠性。
\end{itemize}

项目成果:
AI LQA 模型的判错二分类Recall与Precision均稳定提升至 90\% 以上，远超基线水平 (<64\%)。

\textbf{基于 LLM 的 AI翻译调优：}
背景: 旨在直接提升特定业务场景（如 TT 话题/文档、PDI 中英翻译）的机器翻译质量。技术方案:
\begin{itemize}
  \item \textbf{Reward奖励函数设计：} 采用 GRPO,DPO 对 Qwen2.5-7B-Instruct 进行Post-Training，设计结合 BLEU、TER 等自动化指标的句级 Reward 函数，增强其对模型优化方向的引导能力。
  \item \textbf{Agent搭建：} 构建LLM多阶段自主优化翻译流程，引入术语表以及风格指南，融合多维规范的Agent反思与迭代修正核心，引导及规范化译文。
\end{itemize}

项目成果: 人工 LQA 分数稳定提升至 80\% 以上 ，超过基线水平 (<74\%)。

\ResumeItem{\ResumeUrl{https://www.sany.com.cn}{三一集团}}
[耕耘实验室 大模型算法实习]
[2024.05—2025.09]

项目背景：研发工业级多模态智能体系统，通过LLM实现复杂工业流程的智能决策与控制，解决传统方案人工依赖度高、规则泛化性差等痛点。技术方案
\begin{itemize}
  \item \textbf{数据集构建：} 针对含大量无语义标注数据、动作异常等结构性问题的原始数据集，进行有效筛选与过滤；通过对视觉信息随机剪裁等方法实施数据增强。构建PromptBuilder对话管理、系统提示集成和特殊标记处理。
  \item \textbf{模型选择与微调：} 构建了包含SigLIP视觉编码器、Fused-GELU投影层及Qwen2大语言模型的多模态VLA框架。构建Action Tokenizer，创新性采用离散化映射连续动作向量至语言模型词表中Special Token处。
  \item \textbf{模型训练与评估：} 进行全参数端到端SFT，监督模型学习预测动作向量。以L1 Loss，Action Accuracy等作为训练监控指标，最终在Libero Benchmark评估模型预测、执行动作能力。
\end{itemize}

项目成果：模型成功预测动作的准确率达到86\%，内存占用降幅达67\%，有效提升公司焊接工作效率和质量。

% \section{个人总结}
% 本人为中山大学应用统计专业在读硕士研究生，具备扎实的数学统计理论基础和丰富的算法工程实践经验。在快手、字节跳动、三一集团等知名企业的实习经历中，深度参与了广告算法优化、大模型应用、多模态智能体等前沿技术项目，展现出优秀的技术能力和业务理解力。\textbf{技术专长：}机器学习与深度学习（CVR预估、Uplift建模、因果推断等）；大模型技术（LLM微调、多模态模型、Agent系统）；算法工程化（全链路经验）。\textbf{核心优势：}业务导向，能够深入理解业务需求，将复杂的技术方案转化为实际的商业价值；创新能力，善于结合前沿理论与实际问题，提出创新性解决方案；工程实践，具备优秀的代码实现能力和系统设计思维。期待在算法工程师岗位上继续深耕技术，为企业创造更大价值。

\end{document}
